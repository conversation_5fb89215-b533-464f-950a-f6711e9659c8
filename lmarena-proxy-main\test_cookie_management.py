#!/usr/bin/env python3
"""
LMArena Cookie 管理功能测试脚本

使用方法:
python test_cookie_management.py

确保代理服务器正在运行在 localhost:9080
"""

import requests
import json
import time

BASE_URL = "http://localhost:9080"

def test_api_endpoint(method, endpoint, data=None, expected_status=200):
    """测试 API 端点"""
    url = f"{BASE_URL}{endpoint}"
    
    try:
        if method.upper() == "GET":
            response = requests.get(url, timeout=10)
        elif method.upper() == "POST":
            response = requests.post(url, json=data, timeout=10)
        elif method.upper() == "DELETE":
            response = requests.delete(url, timeout=10)
        else:
            print(f"❌ 不支持的 HTTP 方法: {method}")
            return False
        
        if response.status_code == expected_status:
            print(f"✅ {method} {endpoint} - 状态码: {response.status_code}")
            try:
                result = response.json()
                print(f"   响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
                return result
            except:
                print(f"   响应: {response.text[:200]}...")
                return True
        else:
            print(f"❌ {method} {endpoint} - 期望状态码: {expected_status}, 实际: {response.status_code}")
            print(f"   响应: {response.text[:200]}...")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ {method} {endpoint} - 请求失败: {e}")
        return False

def main():
    print("🧪 LMArena Cookie 管理功能测试")
    print("=" * 50)
    
    # 测试服务器连接
    print("\n1. 测试服务器连接...")
    if not test_api_endpoint("GET", "/health"):
        print("❌ 服务器未运行，请先启动代理服务器")
        return
    
    # 测试获取当前 Cookie
    print("\n2. 测试获取当前 Cookie...")
    current_cookies = test_api_endpoint("GET", "/api/lmarena/cookies")
    
    # 测试设置 Cookie
    print("\n3. 测试设置 Cookie...")
    test_cookies = {
        "test_session": "test_value_123",
        "test_auth": "auth_token_456"
    }
    
    set_result = test_api_endpoint("POST", "/api/lmarena/cookies", {
        "cookies": test_cookies
    })
    
    if set_result:
        print("   ✅ Cookie 设置成功")
    
    # 验证 Cookie 是否保存
    print("\n4. 验证 Cookie 保存...")
    saved_cookies = test_api_endpoint("GET", "/api/lmarena/cookies")
    
    if saved_cookies and saved_cookies.get("cookies") == test_cookies:
        print("   ✅ Cookie 保存验证成功")
    else:
        print("   ❌ Cookie 保存验证失败")
    
    # 测试连接测试功能
    print("\n5. 测试 LMArena 连接...")
    connection_result = test_api_endpoint("POST", "/api/lmarena/test-connection")
    
    if connection_result:
        if connection_result.get("success"):
            print("   ✅ LMArena 连接测试成功")
        else:
            print(f"   ⚠️ LMArena 连接测试失败: {connection_result.get('error')}")
    
    # 测试清除 Cookie
    print("\n6. 测试清除 Cookie...")
    clear_result = test_api_endpoint("DELETE", "/api/lmarena/cookies")
    
    if clear_result:
        print("   ✅ Cookie 清除成功")
    
    # 验证 Cookie 是否清除
    print("\n7. 验证 Cookie 清除...")
    cleared_cookies = test_api_endpoint("GET", "/api/lmarena/cookies")
    
    if cleared_cookies and len(cleared_cookies.get("cookies", {})) == 0:
        print("   ✅ Cookie 清除验证成功")
    else:
        print("   ❌ Cookie 清除验证失败")
    
    # 测试配置 API
    print("\n8. 测试配置 API...")
    config_result = test_api_endpoint("GET", "/api/config")
    
    if config_result and "lmarena" in config_result:
        print("   ✅ 配置 API 包含 LMArena 设置")
    else:
        print("   ❌ 配置 API 缺少 LMArena 设置")
    
    # 测试系统信息 API
    print("\n9. 测试系统信息 API...")
    system_info = test_api_endpoint("GET", "/api/system/info")
    
    if system_info:
        print("   ✅ 系统信息 API 正常")
    
    print("\n" + "=" * 50)
    print("🎉 测试完成！")
    print("\n💡 提示:")
    print("- 访问 http://localhost:9080/monitor 查看监控面板")
    print("- 在系统设置中找到 'LMArena Cookie 管理' 部分")
    print("- 使用界面测试 Cookie 管理功能")

if __name__ == "__main__":
    main()
