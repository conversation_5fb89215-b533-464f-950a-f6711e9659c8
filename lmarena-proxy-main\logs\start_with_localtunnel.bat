@echo off
echo ========================================
echo   LMArena 代理服务器 + LocalTunnel
echo ========================================
echo.

echo 步骤 1: 安装 LocalTunnel
echo.
call npm install -g localtunnel
if %errorlevel% neq 0 (
    echo 警告: LocalTunnel 安装可能失败，但继续启动...
) else (
    echo ✅ LocalTunnel 安装成功
)

echo.
echo 步骤 2: 安装 Python 依赖
pip install -r requirements.txt >nul 2>&1

echo.
echo 步骤 3: 启动服务器
echo ========================================
echo 服务器启动中...
echo LocalTunnel 将自动启动并显示外网地址
echo ========================================
echo.

python proxy_server.py

pause
