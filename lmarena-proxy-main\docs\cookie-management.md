# LMArena Cookie 管理功能

## 概述

本功能为 LMArena 代理服务器增加了 Cookie 管理功能，允许用户通过监控面板管理与 https://lmarena.ai/ 的认证状态。

## 功能特性

### 1. 自动认证管理
- 自动检测和维护 LMArena 认证状态
- 可配置的认证检查间隔
- 与现有浏览器扩展集成

### 2. Cookie 管理界面
- 在监控面板中提供直观的 Cookie 管理界面
- 显示当前 Cookie 状态和数量
- 支持手动设置和清除 Cookie

### 3. 连接测试
- 一键测试与 LMArena 的连接状态
- 检测常见的认证问题
- 提供详细的错误信息和建议

## 使用方法

### 1. 访问 Cookie 管理界面

1. 打开监控面板：`http://your-server:9080/monitor`
2. 点击"系统设置"部分的"显示/隐藏"按钮
3. 找到"🍪 LMArena Cookie 管理"部分

### 2. 自动认证设置

- **自动认证**：启用后系统会自动处理认证流程
- **认证检查间隔**：设置检查认证状态的时间间隔（秒）

### 3. 手动设置 Cookie

如果自动认证失败，可以手动设置 Cookie：

1. 在浏览器中访问 https://lmarena.ai/
2. 完成登录或注册
3. 按 F12 打开开发者工具
4. 转到 Application/存储 → Cookies → lmarena.ai
5. 复制需要的 Cookie 值
6. 在监控面板中粘贴到"手动设置 Cookie"文本框
7. 点击"保存 Cookie 设置"

### 4. 测试连接

点击"测试连接"按钮可以验证：
- Cookie 是否有效
- 是否遇到 Cloudflare 验证
- 是否需要重新登录

### 5. 清除 Cookie

如果遇到认证问题，可以点击"清除 Cookie"重置认证状态。

## API 端点

### GET /api/lmarena/cookies
获取当前保存的 Cookie

**响应示例：**
```json
{
  "cookies": {
    "session_id": "abc123...",
    "auth_token": "xyz789..."
  },
  "count": 2,
  "auto_auth": true
}
```

### POST /api/lmarena/cookies
设置 Cookie

**请求体：**
```json
{
  "cookies": {
    "session_id": "abc123...",
    "auth_token": "xyz789..."
  }
}
```

### DELETE /api/lmarena/cookies
清除所有 Cookie

### POST /api/lmarena/test-connection
测试与 LMArena 的连接

**响应示例：**
```json
{
  "success": true,
  "message": "连接成功 (状态码: 200)"
}
```

## 配置文件

Cookie 设置保存在 `logs/config.json` 中：

```json
{
  "lmarena": {
    "cookies": {
      "session_id": "...",
      "auth_token": "..."
    },
    "auto_auth": true,
    "auth_check_interval": 300
  }
}
```

## 故障排除

### 常见问题

1. **连接测试失败**
   - 检查网络连接
   - 确认 Cookie 是否过期
   - 尝试在浏览器中重新登录

2. **Cloudflare 验证**
   - 需要通过浏览器完成验证
   - 验证完成后重新获取 Cookie

3. **认证状态丢失**
   - 检查 Cookie 是否正确设置
   - 确认自动认证是否启用
   - 查看服务器日志获取详细信息

### 日志查看

相关日志会记录在服务器控制台和 `logs/server.log` 中，搜索关键词：
- `LMArena Cookie`
- `认证`
- `Cookie`

## 安全注意事项

1. Cookie 包含敏感的认证信息，请妥善保管
2. 不要在不安全的网络环境中传输 Cookie
3. 定期更新 Cookie 以保持安全性
4. 如果怀疑 Cookie 泄露，请立即清除并重新登录

## 与浏览器扩展的集成

新的 Cookie 管理功能与现有的浏览器扩展 (`lmarena_injector.user.js`) 完全兼容：

- 扩展会自动使用服务器端保存的 Cookie
- 如果服务器端没有 Cookie，扩展会尝试自动认证
- 认证成功后，Cookie 会同步到服务器端

这确保了无缝的用户体验和可靠的认证状态管理。
