{"network": {"manual_ip": null, "port": 9080, "auto_detect_ip": true}, "request": {"timeout_seconds": 180, "max_concurrent_requests": 20, "backpressure_queue_size": 5}, "monitoring": {"error_rate_threshold": 0.1, "response_time_threshold": 30, "active_requests_threshold": 50, "cleanup_interval": 300}, "quick_links": [{"name": "监控面板", "url": "/monitor", "icon": "📊"}, {"name": "健康检查", "url": "/api/health/detailed", "icon": "🏥"}, {"name": "Prometheus", "url": "/metrics", "icon": "📈"}, {"name": "API文档", "url": "/monitor#api-docs", "icon": "📚"}], "ngrok": {"enabled": true, "authtoken": "*************************************************", "static_domain": "tahr-mutual-emu.ngrok-free.app"}, "localtunnel": {"enabled": false, "subdomain": "lmarena-proxy", "auto_install": true}, "serveo": {"enabled": false, "subdomain": "lmarena-proxy"}}