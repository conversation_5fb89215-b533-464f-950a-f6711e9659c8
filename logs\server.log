16:48:45.135 - INFO - [lifespan:1746] - 服务器正在启动...
16:48:45.144 - INFO - [lifespan:1822] - 🌐 Server access URLs:
16:48:45.144 - INFO - [lifespan:1823] -   - Local: http://localhost:9080
16:48:45.144 - INFO - [lifespan:1824] -   - Network: http://*************:9080
16:48:45.144 - INFO - [lifespan:1833] - 📱 Use the Network URL to access from your phone on the same WiFi
16:48:45.146 - INFO - [lifespan:1839] - 
📋 Available Endpoints:
16:48:45.146 - INFO - [lifespan:1840] -   🖥️  Monitor Dashboard: http://*************:9080/monitor
16:48:45.147 - INFO - [lifespan:1841] -      实时监控面板，查看系统状态、请求日志、性能指标
16:48:45.147 - INFO - [lifespan:1843] - 
  📊 Metrics & Health:
16:48:45.148 - INFO - [lifespan:1844] -      - Prometheus Metrics: http://*************:9080/metrics
16:48:45.148 - INFO - [lifespan:1845] -        Prometheus格式的性能指标，可接入Grafana
16:48:45.148 - INFO - [lifespan:1846] -      - Health Check: http://*************:9080/health
16:48:45.148 - INFO - [lifespan:1847] -        基础健康检查
16:48:45.148 - INFO - [lifespan:1848] -      - Detailed Health: http://*************:9080/api/health/detailed
16:48:45.149 - INFO - [lifespan:1849] -        详细健康状态，包含评分和建议
16:48:45.149 - INFO - [lifespan:1851] - 
  🤖 AI API:
16:48:45.149 - INFO - [lifespan:1852] -      - Chat Completions: POST http://*************:9080/v1/chat/completions
16:48:45.149 - INFO - [lifespan:1853] -        OpenAI兼容的聊天API
16:48:45.149 - INFO - [lifespan:1854] -      - List Models: GET http://*************:9080/v1/models
16:48:45.149 - INFO - [lifespan:1855] -        获取可用模型列表
16:48:45.150 - INFO - [lifespan:1856] -      - Refresh Models: POST http://*************:9080/v1/refresh-models
16:48:45.150 - INFO - [lifespan:1857] -        刷新模型列表
16:48:45.150 - INFO - [lifespan:1859] - 
  📈 Statistics:
16:48:45.150 - INFO - [lifespan:1860] -      - Stats Summary: http://*************:9080/api/stats/summary
16:48:45.150 - INFO - [lifespan:1861] -        24小时统计摘要
16:48:45.150 - INFO - [lifespan:1862] -      - Request Logs: http://*************:9080/api/logs/requests
16:48:45.150 - INFO - [lifespan:1863] -        请求日志API
16:48:45.150 - INFO - [lifespan:1864] -      - Error Logs: http://*************:9080/api/logs/errors
16:48:45.150 - INFO - [lifespan:1865] -        错误日志API
16:48:45.152 - INFO - [lifespan:1866] -      - Alerts: http://*************:9080/api/alerts
16:48:45.152 - INFO - [lifespan:1867] -        系统告警历史
16:48:45.152 - INFO - [lifespan:1869] - 
  🛠️  OpenAI Client Config:
16:48:45.152 - INFO - [lifespan:1870] -      base_url='http://*************:9080/v1'
16:48:45.152 - INFO - [lifespan:1871] -      api_key='sk-any-string-you-like'
16:48:45.153 - INFO - [lifespan:1872] - 
============================================================

16:48:45.153 - INFO - [lifespan:1877] - 已加载 85 个备用模型
16:48:45.154 - INFO - [lifespan:1886] - 服务器启动完成
16:48:45.154 - INFO - [periodic_cleanup:1281] - 清理任务执行完成. 活跃请求: 0
16:48:45.190 - INFO - [websocket_endpoint:1952] - ✅ 浏览器WebSocket已连接
16:48:45.194 - INFO - [websocket_endpoint:1987] - 🤝 收到重连握手，浏览器有 0 个待处理请求
16:48:45.597 - INFO - [websocket_endpoint:1952] - ✅ 浏览器WebSocket已连接
16:48:45.600 - INFO - [websocket_endpoint:1987] - 🤝 收到重连握手，浏览器有 0 个待处理请求
16:49:24.121 - INFO - [save_config:128] - 配置已保存
16:49:33.767 - WARNING - [websocket_endpoint:2055] - ❌ 浏览器客户端已断开连接
16:49:33.768 - INFO - [websocket_endpoint:2075] - WebSocket cleaned up. Persistent requests kept alive.
16:49:33.768 - WARNING - [websocket_endpoint:2055] - ❌ 浏览器客户端已断开连接
16:49:33.768 - INFO - [websocket_endpoint:2075] - WebSocket cleaned up. Persistent requests kept alive.
16:49:33.875 - INFO - [lifespan:1893] - 生命周期: 服务器正在关闭。正在取消 4 个后台任务...
16:49:33.875 - INFO - [lifespan:1899] - 生命周期: 正在取消任务: <Task pending name='Task-4' coro=<MonitoringAlerts.check_system_health() running at d:\aa1\lmarena-proxy-main\proxy_server.py:1151> wait_for=<Future pending cb=[Task.task_wakeup()]>>
16:49:33.876 - INFO - [lifespan:1899] - 生命周期: 正在取消任务: <Task pending name='Task-10' coro=<WebSocketHeartbeat.start_heartbeat() running at d:\aa1\lmarena-proxy-main\proxy_server.py:1113> wait_for=<Future pending cb=[Task.task_wakeup()]>>
16:49:33.876 - INFO - [lifespan:1899] - 生命周期: 正在取消任务: <Task pending name='Task-3' coro=<periodic_cleanup() running at d:\aa1\lmarena-proxy-main\proxy_server.py:1286> wait_for=<Future pending cb=[Task.task_wakeup()]>>
16:49:33.876 - INFO - [lifespan:1899] - 生命周期: 正在取消任务: <Task pending name='Task-17' coro=<WebSocketHeartbeat.start_heartbeat() running at d:\aa1\lmarena-proxy-main\proxy_server.py:1113> wait_for=<Future pending cb=[Task.task_wakeup()]>>
16:49:33.877 - INFO - [lifespan:1905] - 生命周期: 等待 4 个已取消的任务完成...
16:49:33.877 - INFO - [lifespan:1911] - 生命周期: 任务 0 正常完成
16:49:33.877 - INFO - [lifespan:1911] - 生命周期: 任务 1 正常完成
16:49:33.877 - INFO - [lifespan:1911] - 生命周期: 任务 2 正常完成
16:49:33.878 - INFO - [lifespan:1911] - 生命周期: 任务 3 正常完成
16:49:33.878 - INFO - [lifespan:1913] - 生命周期: 所有后台任务已取消。关闭完成。
