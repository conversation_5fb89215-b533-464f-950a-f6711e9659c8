# 故障排除指南

## 🚨 常见问题解决方案

### 1. npm 命令未找到

**问题**: 启动时提示 "npm 命令未找到"

**解决方案**:
- **方案A**: 安装 Node.js
  1. 运行 `ai_test\install_nodejs.bat` 自动安装
  2. 或手动访问 https://nodejs.org 下载安装
  3. 安装后重启命令提示符

- **方案B**: 使用 Serveo (推荐，无需安装)
  1. 运行 `start_simple.bat` 
  2. 系统会自动使用 Serveo 进行内网穿透

### 2. 软件安装失败

**问题**: LocalTunnel 或其他软件安装失败

**解决方案**:
1. 检查网络连接
2. 使用管理员权限运行命令提示符
3. 或者禁用 LocalTunnel，启用 Serveo:
   ```
   访问 http://localhost:9080/monitor
   在设置中禁用 LocalTunnel，启用 Serveo
   ```

### 3. SSH 连接失败 (Serveo)

**问题**: Serveo 启动失败，提示 SSH 相关错误

**解决方案**:
1. 确保防火墙允许 SSH 连接
2. 检查网络是否支持 SSH (端口 22)
3. 如果在公司网络，可能需要使用代理

### 4. 端口被占用

**问题**: 启动时提示端口 9080 被占用

**解决方案**:
1. 查找占用进程: `netstat -ano | findstr :9080`
2. 结束占用进程: `taskkill /PID <进程ID> /F`
3. 或修改配置文件中的端口号

### 5. 内网穿透无法访问

**问题**: 获得了外网地址但无法访问

**解决方案**:
1. 检查本地服务器是否正常运行
2. 确认防火墙设置
3. 尝试不同的内网穿透服务:
   - Serveo (基于 SSH，最稳定)
   - LocalTunnel (需要 Node.js)
   - ngrok (需要注册)

## 🔧 推荐配置

### 最简单的配置 (推荐新手)
```json
{
  "ngrok": {"enabled": false},
  "localtunnel": {"enabled": false},
  "serveo": {"enabled": true, "subdomain": "my-app"}
}
```

### 高级配置 (有 Node.js 环境)
```json
{
  "ngrok": {"enabled": false},
  "localtunnel": {"enabled": true, "subdomain": "my-app"},
  "serveo": {"enabled": false}
}
```

## 📞 获取帮助

如果问题仍未解决:
1. 查看服务器日志: `logs/server.log`
2. 检查错误日志: `logs/errors.jsonl`
3. 运行测试脚本: `ai_test\test_localtunnel_integration.bat`
