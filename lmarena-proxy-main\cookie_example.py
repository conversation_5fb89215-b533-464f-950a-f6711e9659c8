#!/usr/bin/env python3
"""
LMArena Cookie 管理使用示例

这个脚本演示如何通过 API 管理 LMArena Cookie
"""

import requests
import json

# 代理服务器地址
PROXY_URL = "http://localhost:9080"

def get_cookies():
    """获取当前保存的 Cookie"""
    try:
        response = requests.get(f"{PROXY_URL}/api/lmarena/cookies")
        if response.status_code == 200:
            data = response.json()
            print(f"当前保存了 {data['count']} 个 Cookie:")
            for name, value in data['cookies'].items():
                print(f"  {name}: {value[:20]}...")
            return data['cookies']
        else:
            print(f"获取 Cookie 失败: {response.status_code}")
            return {}
    except Exception as e:
        print(f"获取 Cookie 出错: {e}")
        return {}

def set_cookies(cookies_dict):
    """设置 Cookie"""
    try:
        response = requests.post(
            f"{PROXY_URL}/api/lmarena/cookies",
            json={"cookies": cookies_dict}
        )
        if response.status_code == 200:
            result = response.json()
            print(f"✅ {result['message']}")
            return True
        else:
            print(f"❌ 设置 Cookie 失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 设置 Cookie 出错: {e}")
        return False

def test_connection():
    """测试与 LMArena 的连接"""
    try:
        response = requests.post(f"{PROXY_URL}/api/lmarena/test-connection")
        if response.status_code == 200:
            result = response.json()
            if result['success']:
                print(f"✅ 连接成功: {result['message']}")
            else:
                print(f"❌ 连接失败: {result['error']}")
            return result['success']
        else:
            print(f"❌ 测试连接失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 测试连接出错: {e}")
        return False

def clear_cookies():
    """清除所有 Cookie"""
    try:
        response = requests.delete(f"{PROXY_URL}/api/lmarena/cookies")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ {result['message']}")
            return True
        else:
            print(f"❌ 清除 Cookie 失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 清除 Cookie 出错: {e}")
        return False

def parse_cookie_string(cookie_string):
    """解析浏览器复制的 Cookie 字符串"""
    cookies = {}
    if cookie_string:
        pairs = cookie_string.split(';')
        for pair in pairs:
            if '=' in pair:
                name, value = pair.split('=', 1)
                cookies[name.strip()] = value.strip()
    return cookies

def main():
    print("🍪 LMArena Cookie 管理示例")
    print("=" * 40)
    
    while True:
        print("\n选择操作:")
        print("1. 查看当前 Cookie")
        print("2. 设置 Cookie")
        print("3. 从浏览器导入 Cookie")
        print("4. 测试连接")
        print("5. 清除 Cookie")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-5): ").strip()
        
        if choice == "0":
            print("👋 再见!")
            break
        elif choice == "1":
            print("\n📋 当前 Cookie:")
            get_cookies()
        elif choice == "2":
            print("\n✏️ 手动设置 Cookie:")
            name = input("Cookie 名称: ").strip()
            value = input("Cookie 值: ").strip()
            if name and value:
                set_cookies({name: value})
            else:
                print("❌ Cookie 名称和值不能为空")
        elif choice == "3":
            print("\n📥 从浏览器导入 Cookie:")
            print("请从浏览器开发者工具中复制 Cookie 字符串")
            print("格式: name1=value1; name2=value2; ...")
            cookie_string = input("Cookie 字符串: ").strip()
            if cookie_string:
                cookies = parse_cookie_string(cookie_string)
                if cookies:
                    print(f"解析到 {len(cookies)} 个 Cookie:")
                    for name, value in cookies.items():
                        print(f"  {name}: {value[:30]}...")
                    confirm = input("确认设置这些 Cookie? (y/N): ").strip().lower()
                    if confirm == 'y':
                        set_cookies(cookies)
                else:
                    print("❌ 未能解析到有效的 Cookie")
            else:
                print("❌ Cookie 字符串不能为空")
        elif choice == "4":
            print("\n🔗 测试 LMArena 连接:")
            test_connection()
        elif choice == "5":
            print("\n🗑️ 清除 Cookie:")
            confirm = input("确认清除所有 Cookie? (y/N): ").strip().lower()
            if confirm == 'y':
                clear_cookies()
            else:
                print("❌ 操作已取消")
        else:
            print("❌ 无效选择，请重试")

if __name__ == "__main__":
    main()
