22:39:49.096 - INFO - [lifespan:1173] - 服务器正在启动...
22:39:49.097 - INFO - [lifespan:1178] - 🌐 Server access URLs:
22:39:49.097 - INFO - [lifespan:1179] -   - Local: http://localhost:9080
22:39:49.097 - INFO - [lifespan:1180] -   - Network: http://127.0.0.1:9080
22:39:49.097 - INFO - [lifespan:1181] - 📱 Use the Network URL to access from your phone on the same WiFi
22:39:49.097 - INFO - [lifespan:1184] - 
📋 Available Endpoints:
22:39:49.098 - INFO - [lifespan:1185] -   🖥️  Monitor Dashboard: http://127.0.0.1:9080/monitor
22:39:49.098 - INFO - [lifespan:1186] -      实时监控面板，查看系统状态、请求日志、性能指标
22:39:49.098 - INFO - [lifespan:1188] - 
  📊 Metrics & Health:
22:39:49.098 - INFO - [lifespan:1189] -      - Prometheus Metrics: http://127.0.0.1:9080/metrics
22:39:49.098 - INFO - [lifespan:1190] -        Prometheus格式的性能指标，可接入Grafana
22:39:49.098 - INFO - [lifespan:1191] -      - Health Check: http://127.0.0.1:9080/health
22:39:49.098 - INFO - [lifespan:1192] -        基础健康检查
22:39:49.098 - INFO - [lifespan:1193] -      - Detailed Health: http://127.0.0.1:9080/api/health/detailed
22:39:49.098 - INFO - [lifespan:1194] -        详细健康状态，包含评分和建议
22:39:49.098 - INFO - [lifespan:1196] - 
  🤖 AI API:
22:39:49.098 - INFO - [lifespan:1197] -      - Chat Completions: POST http://127.0.0.1:9080/v1/chat/completions
22:39:49.098 - INFO - [lifespan:1198] -        OpenAI兼容的聊天API
22:39:49.099 - INFO - [lifespan:1199] -      - List Models: GET http://127.0.0.1:9080/v1/models
22:39:49.099 - INFO - [lifespan:1200] -        获取可用模型列表
22:39:49.099 - INFO - [lifespan:1201] -      - Refresh Models: POST http://127.0.0.1:9080/v1/refresh-models
22:39:49.099 - INFO - [lifespan:1202] -        刷新模型列表
22:39:49.100 - INFO - [lifespan:1204] - 
  📈 Statistics:
22:39:49.100 - INFO - [lifespan:1205] -      - Stats Summary: http://127.0.0.1:9080/api/stats/summary
22:39:49.100 - INFO - [lifespan:1206] -        24小时统计摘要
22:39:49.100 - INFO - [lifespan:1207] -      - Request Logs: http://127.0.0.1:9080/api/logs/requests
22:39:49.100 - INFO - [lifespan:1208] -        请求日志API
22:39:49.100 - INFO - [lifespan:1209] -      - Error Logs: http://127.0.0.1:9080/api/logs/errors
22:39:49.100 - INFO - [lifespan:1210] -        错误日志API
22:39:49.100 - INFO - [lifespan:1211] -      - Alerts: http://127.0.0.1:9080/api/alerts
22:39:49.100 - INFO - [lifespan:1212] -        系统告警历史
22:39:49.100 - INFO - [lifespan:1214] - 
  🛠️  OpenAI Client Config:
22:39:49.100 - INFO - [lifespan:1215] -      base_url='http://127.0.0.1:9080/v1'
22:39:49.100 - INFO - [lifespan:1216] -      api_key='sk-any-string-you-like'
22:39:49.101 - INFO - [lifespan:1217] - 
============================================================

22:39:49.101 - INFO - [lifespan:1222] - 已加载 85 个备用模型
22:39:49.101 - INFO - [lifespan:1231] - 服务器启动完成
22:39:49.102 - INFO - [periodic_cleanup:708] - 清理任务执行完成. 活跃请求: 0
22:44:49.110 - INFO - [periodic_cleanup:708] - 清理任务执行完成. 活跃请求: 0
22:45:19.162 - WARNING - [send_alert:660] - 系统告警: 浏览器已断线 5 分钟
22:45:25.449 - INFO - [lifespan:1173] - 服务器正在启动...
22:45:25.451 - INFO - [lifespan:1178] - 🌐 Server access URLs:
22:45:25.451 - INFO - [lifespan:1179] -   - Local: http://localhost:9080
22:45:25.451 - INFO - [lifespan:1180] -   - Network: http://127.0.0.1:9080
22:45:25.451 - INFO - [lifespan:1181] - 📱 Use the Network URL to access from your phone on the same WiFi
22:45:25.451 - INFO - [lifespan:1184] - 
📋 Available Endpoints:
22:45:25.451 - INFO - [lifespan:1185] -   🖥️  Monitor Dashboard: http://127.0.0.1:9080/monitor
22:45:25.452 - INFO - [lifespan:1186] -      实时监控面板，查看系统状态、请求日志、性能指标
22:45:25.452 - INFO - [lifespan:1188] - 
  📊 Metrics & Health:
22:45:25.452 - INFO - [lifespan:1189] -      - Prometheus Metrics: http://127.0.0.1:9080/metrics
22:45:25.452 - INFO - [lifespan:1190] -        Prometheus格式的性能指标，可接入Grafana
22:45:25.452 - INFO - [lifespan:1191] -      - Health Check: http://127.0.0.1:9080/health
22:45:25.452 - INFO - [lifespan:1192] -        基础健康检查
22:45:25.452 - INFO - [lifespan:1193] -      - Detailed Health: http://127.0.0.1:9080/api/health/detailed
22:45:25.452 - INFO - [lifespan:1194] -        详细健康状态，包含评分和建议
22:45:25.452 - INFO - [lifespan:1196] - 
  🤖 AI API:
22:45:25.452 - INFO - [lifespan:1197] -      - Chat Completions: POST http://127.0.0.1:9080/v1/chat/completions
22:45:25.452 - INFO - [lifespan:1198] -        OpenAI兼容的聊天API
22:45:25.453 - INFO - [lifespan:1199] -      - List Models: GET http://127.0.0.1:9080/v1/models
22:45:25.453 - INFO - [lifespan:1200] -        获取可用模型列表
22:45:25.453 - INFO - [lifespan:1201] -      - Refresh Models: POST http://127.0.0.1:9080/v1/refresh-models
22:45:25.453 - INFO - [lifespan:1202] -        刷新模型列表
22:45:25.453 - INFO - [lifespan:1204] - 
  📈 Statistics:
22:45:25.453 - INFO - [lifespan:1205] -      - Stats Summary: http://127.0.0.1:9080/api/stats/summary
22:45:25.453 - INFO - [lifespan:1206] -        24小时统计摘要
22:45:25.453 - INFO - [lifespan:1207] -      - Request Logs: http://127.0.0.1:9080/api/logs/requests
22:45:25.453 - INFO - [lifespan:1208] -        请求日志API
22:45:25.453 - INFO - [lifespan:1209] -      - Error Logs: http://127.0.0.1:9080/api/logs/errors
22:45:25.453 - INFO - [lifespan:1210] -        错误日志API
22:45:25.453 - INFO - [lifespan:1211] -      - Alerts: http://127.0.0.1:9080/api/alerts
22:45:25.453 - INFO - [lifespan:1212] -        系统告警历史
22:45:25.453 - INFO - [lifespan:1214] - 
  🛠️  OpenAI Client Config:
22:45:25.453 - INFO - [lifespan:1215] -      base_url='http://127.0.0.1:9080/v1'
22:45:25.453 - INFO - [lifespan:1216] -      api_key='sk-any-string-you-like'
22:45:25.454 - INFO - [lifespan:1217] - 
============================================================

22:45:25.454 - INFO - [lifespan:1222] - 已加载 85 个备用模型
22:45:25.454 - INFO - [lifespan:1231] - 服务器启动完成
22:45:25.454 - INFO - [periodic_cleanup:708] - 清理任务执行完成. 活跃请求: 0
22:50:25.478 - INFO - [periodic_cleanup:708] - 清理任务执行完成. 活跃请求: 0
22:50:55.569 - WARNING - [send_alert:660] - 系统告警: 浏览器已断线 5 分钟
22:51:25.560 - WARNING - [send_alert:660] - 系统告警: 浏览器已断线 5 分钟
22:51:47.119 - INFO - [websocket_endpoint:1278] - ✅ 浏览器WebSocket已连接
22:51:47.282 - INFO - [websocket_endpoint:1313] - 🤝 收到重连握手，浏览器有 0 个待处理请求
22:52:16.709 - INFO - [lifespan:1173] - 服务器正在启动...
22:52:16.710 - INFO - [lifespan:1178] - 🌐 Server access URLs:
22:52:16.710 - INFO - [lifespan:1179] -   - Local: http://localhost:9080
22:52:16.710 - INFO - [lifespan:1180] -   - Network: http://127.0.0.1:9080
22:52:16.710 - INFO - [lifespan:1181] - 📱 Use the Network URL to access from your phone on the same WiFi
22:52:16.710 - INFO - [lifespan:1184] - 
📋 Available Endpoints:
22:52:16.710 - INFO - [lifespan:1185] -   🖥️  Monitor Dashboard: http://127.0.0.1:9080/monitor
22:52:16.710 - INFO - [lifespan:1186] -      实时监控面板，查看系统状态、请求日志、性能指标
22:52:16.711 - INFO - [lifespan:1188] - 
  📊 Metrics & Health:
22:52:16.711 - INFO - [lifespan:1189] -      - Prometheus Metrics: http://127.0.0.1:9080/metrics
22:52:16.711 - INFO - [lifespan:1190] -        Prometheus格式的性能指标，可接入Grafana
22:52:16.711 - INFO - [lifespan:1191] -      - Health Check: http://127.0.0.1:9080/health
22:52:16.711 - INFO - [lifespan:1192] -        基础健康检查
22:52:16.711 - INFO - [lifespan:1193] -      - Detailed Health: http://127.0.0.1:9080/api/health/detailed
22:52:16.711 - INFO - [lifespan:1194] -        详细健康状态，包含评分和建议
22:52:16.711 - INFO - [lifespan:1196] - 
  🤖 AI API:
22:52:16.712 - INFO - [lifespan:1197] -      - Chat Completions: POST http://127.0.0.1:9080/v1/chat/completions
22:52:16.712 - INFO - [lifespan:1198] -        OpenAI兼容的聊天API
22:52:16.712 - INFO - [lifespan:1199] -      - List Models: GET http://127.0.0.1:9080/v1/models
22:52:16.712 - INFO - [lifespan:1200] -        获取可用模型列表
22:52:16.712 - INFO - [lifespan:1201] -      - Refresh Models: POST http://127.0.0.1:9080/v1/refresh-models
22:52:16.713 - INFO - [lifespan:1202] -        刷新模型列表
22:52:16.713 - INFO - [lifespan:1204] - 
  📈 Statistics:
22:52:16.713 - INFO - [lifespan:1205] -      - Stats Summary: http://127.0.0.1:9080/api/stats/summary
22:52:16.713 - INFO - [lifespan:1206] -        24小时统计摘要
22:52:16.713 - INFO - [lifespan:1207] -      - Request Logs: http://127.0.0.1:9080/api/logs/requests
22:52:16.714 - INFO - [lifespan:1208] -        请求日志API
22:52:16.714 - INFO - [lifespan:1209] -      - Error Logs: http://127.0.0.1:9080/api/logs/errors
22:52:16.714 - INFO - [lifespan:1210] -        错误日志API
22:52:16.714 - INFO - [lifespan:1211] -      - Alerts: http://127.0.0.1:9080/api/alerts
22:52:16.714 - INFO - [lifespan:1212] -        系统告警历史
22:52:16.714 - INFO - [lifespan:1214] - 
  🛠️  OpenAI Client Config:
22:52:16.714 - INFO - [lifespan:1215] -      base_url='http://127.0.0.1:9080/v1'
22:52:16.714 - INFO - [lifespan:1216] -      api_key='sk-any-string-you-like'
22:52:16.714 - INFO - [lifespan:1217] - 
============================================================

22:52:16.716 - INFO - [lifespan:1222] - 已加载 85 个备用模型
22:52:16.716 - INFO - [lifespan:1231] - 服务器启动完成
22:52:16.716 - INFO - [periodic_cleanup:708] - 清理任务执行完成. 活跃请求: 0
22:52:17.227 - INFO - [websocket_endpoint:1278] - ✅ 浏览器WebSocket已连接
22:52:17.234 - INFO - [websocket_endpoint:1313] - 🤝 收到重连握手，浏览器有 0 个待处理请求
22:52:17.243 - INFO - [websocket_endpoint:1278] - ✅ 浏览器WebSocket已连接
22:52:17.456 - INFO - [websocket_endpoint:1313] - 🤝 收到重连握手，浏览器有 0 个待处理请求
22:52:25.698 - INFO - [add_request:794] - REQUEST_MGR: Added request f3d391d2-05be-499e-9afc-d612d48e39d1 for tracking
22:52:25.700 - INFO - [chat_completions:1481] - API [ID: f3d391d2-05be-499e-9afc-d612d48e39d1]: Created persistent request for model type 'chat'.
22:52:25.700 - INFO - [chat_completions:1496] - API [ID: f3d391d2-05be-499e-9afc-d612d48e39d1]: Returning text/event-stream response to client.
22:52:25.706 - INFO - [create_lmarena_request_body:1984] - Added empty user message after last user message at index 0 for chat model
22:52:25.706 - INFO - [send_to_browser_task:1548] - TASK [ID: f3d391d2-05be-499e-9afc-d612d48e39d1]: Sending payload and 0 file(s) to browser.
22:52:25.706 - INFO - [send_to_browser_task:1553] - TASK [ID: f3d391d2-05be-499e-9afc-d612d48e39d1]: Payload sent and marked as sent to browser.
22:52:25.707 - INFO - [stream_generator:1597] - STREAMER [ID: f3d391d2-05be-499e-9afc-d612d48e39d1]: Generator started for model type 'chat'.
22:52:28.647 - INFO - [complete_request:841] - REQUEST_MGR: Request f3d391d2-05be-499e-9afc-d612d48e39d1 completed and removed
22:52:28.660 - INFO - [stream_generator:1909] - GENERATOR [ID: f3d391d2-05be-499e-9afc-d612d48e39d1]: Cleaned up response channel.
22:57:16.726 - INFO - [periodic_cleanup:708] - 清理任务执行完成. 活跃请求: 0
23:02:16.725 - INFO - [periodic_cleanup:708] - 清理任务执行完成. 活跃请求: 0
23:05:51.231 - INFO - [add_request:794] - REQUEST_MGR: Added request a6204bca-a06b-4d50-917b-9cd0bbc685b8 for tracking
23:05:51.231 - INFO - [chat_completions:1481] - API [ID: a6204bca-a06b-4d50-917b-9cd0bbc685b8]: Created persistent request for model type 'chat'.
23:05:51.231 - INFO - [chat_completions:1496] - API [ID: a6204bca-a06b-4d50-917b-9cd0bbc685b8]: Returning text/event-stream response to client.
23:05:51.233 - INFO - [create_lmarena_request_body:1984] - Added empty user message after last user message at index 5 for chat model
23:05:51.234 - INFO - [send_to_browser_task:1548] - TASK [ID: a6204bca-a06b-4d50-917b-9cd0bbc685b8]: Sending payload and 0 file(s) to browser.
23:05:51.235 - INFO - [send_to_browser_task:1553] - TASK [ID: a6204bca-a06b-4d50-917b-9cd0bbc685b8]: Payload sent and marked as sent to browser.
23:05:51.236 - INFO - [stream_generator:1597] - STREAMER [ID: a6204bca-a06b-4d50-917b-9cd0bbc685b8]: Generator started for model type 'chat'.
23:06:01.197 - INFO - [complete_request:841] - REQUEST_MGR: Request a6204bca-a06b-4d50-917b-9cd0bbc685b8 completed and removed
23:06:01.231 - INFO - [stream_generator:1909] - GENERATOR [ID: a6204bca-a06b-4d50-917b-9cd0bbc685b8]: Cleaned up response channel.
23:07:16.731 - INFO - [periodic_cleanup:708] - 清理任务执行完成. 活跃请求: 0
23:12:16.744 - INFO - [periodic_cleanup:708] - 清理任务执行完成. 活跃请求: 0
23:17:16.764 - INFO - [periodic_cleanup:708] - 清理任务执行完成. 活跃请求: 0
23:22:16.776 - INFO - [periodic_cleanup:708] - 清理任务执行完成. 活跃请求: 0
01:20:06.669 - INFO - [lifespan:1173] - 服务器正在启动...
01:20:06.671 - INFO - [lifespan:1178] - 🌐 Server access URLs:
01:20:06.672 - INFO - [lifespan:1179] -   - Local: http://localhost:9080
01:20:06.672 - INFO - [lifespan:1180] -   - Network: http://127.0.0.1:9080
01:20:06.672 - INFO - [lifespan:1181] - 📱 Use the Network URL to access from your phone on the same WiFi
01:20:06.672 - INFO - [lifespan:1184] - 
📋 Available Endpoints:
01:20:06.673 - INFO - [lifespan:1185] -   🖥️  Monitor Dashboard: http://127.0.0.1:9080/monitor
01:20:06.673 - INFO - [lifespan:1186] -      实时监控面板，查看系统状态、请求日志、性能指标
01:20:06.673 - INFO - [lifespan:1188] - 
  📊 Metrics & Health:
01:20:06.673 - INFO - [lifespan:1189] -      - Prometheus Metrics: http://127.0.0.1:9080/metrics
01:20:06.673 - INFO - [lifespan:1190] -        Prometheus格式的性能指标，可接入Grafana
01:20:06.675 - INFO - [lifespan:1191] -      - Health Check: http://127.0.0.1:9080/health
01:20:06.675 - INFO - [lifespan:1192] -        基础健康检查
01:20:06.675 - INFO - [lifespan:1193] -      - Detailed Health: http://127.0.0.1:9080/api/health/detailed
01:20:06.675 - INFO - [lifespan:1194] -        详细健康状态，包含评分和建议
01:20:06.676 - INFO - [lifespan:1196] - 
  🤖 AI API:
01:20:06.676 - INFO - [lifespan:1197] -      - Chat Completions: POST http://127.0.0.1:9080/v1/chat/completions
01:20:06.676 - INFO - [lifespan:1198] -        OpenAI兼容的聊天API
01:20:06.676 - INFO - [lifespan:1199] -      - List Models: GET http://127.0.0.1:9080/v1/models
01:20:06.676 - INFO - [lifespan:1200] -        获取可用模型列表
01:20:06.676 - INFO - [lifespan:1201] -      - Refresh Models: POST http://127.0.0.1:9080/v1/refresh-models
01:20:06.676 - INFO - [lifespan:1202] -        刷新模型列表
01:20:06.676 - INFO - [lifespan:1204] - 
  📈 Statistics:
01:20:06.676 - INFO - [lifespan:1205] -      - Stats Summary: http://127.0.0.1:9080/api/stats/summary
01:20:06.676 - INFO - [lifespan:1206] -        24小时统计摘要
01:20:06.678 - INFO - [lifespan:1207] -      - Request Logs: http://127.0.0.1:9080/api/logs/requests
01:20:06.678 - INFO - [lifespan:1208] -        请求日志API
01:20:06.678 - INFO - [lifespan:1209] -      - Error Logs: http://127.0.0.1:9080/api/logs/errors
01:20:06.678 - INFO - [lifespan:1210] -        错误日志API
01:20:06.679 - INFO - [lifespan:1211] -      - Alerts: http://127.0.0.1:9080/api/alerts
01:20:06.679 - INFO - [lifespan:1212] -        系统告警历史
01:20:06.679 - INFO - [lifespan:1214] - 
  🛠️  OpenAI Client Config:
01:20:06.679 - INFO - [lifespan:1215] -      base_url='http://127.0.0.1:9080/v1'
01:20:06.679 - INFO - [lifespan:1216] -      api_key='sk-any-string-you-like'
01:20:06.679 - INFO - [lifespan:1217] - 
============================================================

01:20:06.680 - INFO - [lifespan:1222] - 已加载 85 个备用模型
01:20:06.680 - INFO - [lifespan:1231] - 服务器启动完成
01:20:06.681 - INFO - [periodic_cleanup:708] - 清理任务执行完成. 活跃请求: 0
01:20:07.149 - INFO - [websocket_endpoint:1278] - ✅ 浏览器WebSocket已连接
01:20:07.164 - INFO - [websocket_endpoint:1313] - 🤝 收到重连握手，浏览器有 0 个待处理请求
01:20:07.166 - INFO - [websocket_endpoint:1278] - ✅ 浏览器WebSocket已连接
01:20:07.178 - INFO - [websocket_endpoint:1313] - 🤝 收到重连握手，浏览器有 0 个待处理请求
01:20:10.588 - INFO - [websocket_endpoint:1278] - ✅ 浏览器WebSocket已连接
01:20:10.853 - INFO - [websocket_endpoint:1313] - 🤝 收到重连握手，浏览器有 0 个待处理请求
01:20:12.475 - INFO - [websocket_endpoint:1278] - ✅ 浏览器WebSocket已连接
01:20:12.479 - INFO - [websocket_endpoint:1313] - 🤝 收到重连握手，浏览器有 0 个待处理请求
01:20:20.750 - INFO - [add_request:794] - REQUEST_MGR: Added request 8db9ff45-b017-4978-97d7-643ee15164e4 for tracking
01:20:20.750 - INFO - [chat_completions:1481] - API [ID: 8db9ff45-b017-4978-97d7-643ee15164e4]: Created persistent request for model type 'chat'.
01:20:20.750 - INFO - [chat_completions:1496] - API [ID: 8db9ff45-b017-4978-97d7-643ee15164e4]: Returning text/event-stream response to client.
01:20:20.757 - INFO - [create_lmarena_request_body:1984] - Added empty user message after last user message at index 3 for chat model
01:20:20.757 - INFO - [send_to_browser_task:1548] - TASK [ID: 8db9ff45-b017-4978-97d7-643ee15164e4]: Sending payload and 0 file(s) to browser.
01:20:20.758 - INFO - [send_to_browser_task:1553] - TASK [ID: 8db9ff45-b017-4978-97d7-643ee15164e4]: Payload sent and marked as sent to browser.
01:20:20.759 - INFO - [stream_generator:1597] - STREAMER [ID: 8db9ff45-b017-4978-97d7-643ee15164e4]: Generator started for model type 'chat'.
01:20:21.147 - INFO - [complete_request:841] - REQUEST_MGR: Request 8db9ff45-b017-4978-97d7-643ee15164e4 completed and removed
01:20:21.147 - ERROR - [stream_generator:1670] - STREAMER [ID: 8db9ff45-b017-4978-97d7-643ee15164e4]: Server returned error: {'error': 'Authentication request failed with status 500'}
01:20:21.148 - INFO - [stream_generator:1909] - GENERATOR [ID: 8db9ff45-b017-4978-97d7-643ee15164e4]: Cleaned up response channel.
01:21:06.174 - INFO - [add_request:794] - REQUEST_MGR: Added request 0dddf016-9111-4186-9baf-427dd110d2e5 for tracking
01:21:06.174 - INFO - [chat_completions:1481] - API [ID: 0dddf016-9111-4186-9baf-427dd110d2e5]: Created persistent request for model type 'chat'.
01:21:06.174 - INFO - [chat_completions:1496] - API [ID: 0dddf016-9111-4186-9baf-427dd110d2e5]: Returning text/event-stream response to client.
01:21:06.176 - INFO - [create_lmarena_request_body:1984] - Added empty user message after last user message at index 3 for chat model
01:21:06.176 - INFO - [send_to_browser_task:1548] - TASK [ID: 0dddf016-9111-4186-9baf-427dd110d2e5]: Sending payload and 0 file(s) to browser.
01:21:06.176 - INFO - [send_to_browser_task:1553] - TASK [ID: 0dddf016-9111-4186-9baf-427dd110d2e5]: Payload sent and marked as sent to browser.
01:21:06.177 - INFO - [stream_generator:1597] - STREAMER [ID: 0dddf016-9111-4186-9baf-427dd110d2e5]: Generator started for model type 'chat'.
01:21:06.657 - INFO - [complete_request:841] - REQUEST_MGR: Request 0dddf016-9111-4186-9baf-427dd110d2e5 completed and removed
01:21:06.659 - ERROR - [stream_generator:1670] - STREAMER [ID: 0dddf016-9111-4186-9baf-427dd110d2e5]: Server returned error: {'error': 'Authentication request failed with status 500'}
01:21:06.659 - INFO - [stream_generator:1909] - GENERATOR [ID: 0dddf016-9111-4186-9baf-427dd110d2e5]: Cleaned up response channel.
01:21:19.222 - INFO - [add_request:794] - REQUEST_MGR: Added request 1a3027d5-c775-4ef0-a50e-93003804ed69 for tracking
01:21:19.222 - INFO - [chat_completions:1481] - API [ID: 1a3027d5-c775-4ef0-a50e-93003804ed69]: Created persistent request for model type 'chat'.
01:21:19.223 - INFO - [chat_completions:1496] - API [ID: 1a3027d5-c775-4ef0-a50e-93003804ed69]: Returning text/event-stream response to client.
01:21:19.223 - INFO - [create_lmarena_request_body:1984] - Added empty user message after last user message at index 3 for chat model
01:21:19.224 - INFO - [send_to_browser_task:1548] - TASK [ID: 1a3027d5-c775-4ef0-a50e-93003804ed69]: Sending payload and 0 file(s) to browser.
01:21:19.224 - INFO - [send_to_browser_task:1553] - TASK [ID: 1a3027d5-c775-4ef0-a50e-93003804ed69]: Payload sent and marked as sent to browser.
01:21:19.224 - INFO - [stream_generator:1597] - STREAMER [ID: 1a3027d5-c775-4ef0-a50e-93003804ed69]: Generator started for model type 'chat'.
01:21:19.756 - INFO - [complete_request:841] - REQUEST_MGR: Request 1a3027d5-c775-4ef0-a50e-93003804ed69 completed and removed
01:21:19.756 - ERROR - [stream_generator:1670] - STREAMER [ID: 1a3027d5-c775-4ef0-a50e-93003804ed69]: Server returned error: {'error': 'Authentication request failed with status 500'}
01:21:19.756 - INFO - [stream_generator:1909] - GENERATOR [ID: 1a3027d5-c775-4ef0-a50e-93003804ed69]: Cleaned up response channel.
01:21:48.635 - INFO - [add_request:794] - REQUEST_MGR: Added request e7015d24-30b0-4f90-a22b-75ccb86753dc for tracking
01:21:48.635 - INFO - [chat_completions:1481] - API [ID: e7015d24-30b0-4f90-a22b-75ccb86753dc]: Created persistent request for model type 'chat'.
01:21:48.635 - INFO - [chat_completions:1496] - API [ID: e7015d24-30b0-4f90-a22b-75ccb86753dc]: Returning text/event-stream response to client.
01:21:48.637 - INFO - [create_lmarena_request_body:1984] - Added empty user message after last user message at index 0 for chat model
01:21:48.637 - INFO - [send_to_browser_task:1548] - TASK [ID: e7015d24-30b0-4f90-a22b-75ccb86753dc]: Sending payload and 0 file(s) to browser.
01:21:48.637 - INFO - [send_to_browser_task:1553] - TASK [ID: e7015d24-30b0-4f90-a22b-75ccb86753dc]: Payload sent and marked as sent to browser.
01:21:48.638 - INFO - [stream_generator:1597] - STREAMER [ID: e7015d24-30b0-4f90-a22b-75ccb86753dc]: Generator started for model type 'chat'.
01:21:53.978 - INFO - [complete_request:841] - REQUEST_MGR: Request e7015d24-30b0-4f90-a22b-75ccb86753dc completed and removed
01:21:53.978 - ERROR - [stream_generator:1670] - STREAMER [ID: e7015d24-30b0-4f90-a22b-75ccb86753dc]: Server returned error: {'error': 'Authentication request failed with status 403'}
01:21:53.978 - INFO - [stream_generator:1909] - GENERATOR [ID: e7015d24-30b0-4f90-a22b-75ccb86753dc]: Cleaned up response channel.
01:22:01.336 - INFO - [add_request:794] - REQUEST_MGR: Added request da1ecb52-0bc7-40b8-a4a2-931db33271e5 for tracking
01:22:01.337 - INFO - [chat_completions:1481] - API [ID: da1ecb52-0bc7-40b8-a4a2-931db33271e5]: Created persistent request for model type 'chat'.
01:22:01.337 - INFO - [chat_completions:1496] - API [ID: da1ecb52-0bc7-40b8-a4a2-931db33271e5]: Returning text/event-stream response to client.
01:22:01.337 - INFO - [create_lmarena_request_body:1984] - Added empty user message after last user message at index 0 for chat model
01:22:01.337 - INFO - [send_to_browser_task:1548] - TASK [ID: da1ecb52-0bc7-40b8-a4a2-931db33271e5]: Sending payload and 0 file(s) to browser.
01:22:01.337 - INFO - [send_to_browser_task:1553] - TASK [ID: da1ecb52-0bc7-40b8-a4a2-931db33271e5]: Payload sent and marked as sent to browser.
01:22:01.337 - INFO - [stream_generator:1597] - STREAMER [ID: da1ecb52-0bc7-40b8-a4a2-931db33271e5]: Generator started for model type 'chat'.
01:22:01.443 - INFO - [complete_request:841] - REQUEST_MGR: Request da1ecb52-0bc7-40b8-a4a2-931db33271e5 completed and removed
01:22:01.444 - ERROR - [stream_generator:1670] - STREAMER [ID: da1ecb52-0bc7-40b8-a4a2-931db33271e5]: Server returned error: {'error': 'Authentication request failed with status 403'}
01:22:01.445 - INFO - [stream_generator:1909] - GENERATOR [ID: da1ecb52-0bc7-40b8-a4a2-931db33271e5]: Cleaned up response channel.
01:22:08.649 - INFO - [add_request:794] - REQUEST_MGR: Added request 788bea43-b6b3-4081-b194-05d2044a9956 for tracking
01:22:08.649 - INFO - [chat_completions:1481] - API [ID: 788bea43-b6b3-4081-b194-05d2044a9956]: Created persistent request for model type 'chat'.
01:22:08.650 - INFO - [chat_completions:1496] - API [ID: 788bea43-b6b3-4081-b194-05d2044a9956]: Returning text/event-stream response to client.
01:22:08.650 - INFO - [create_lmarena_request_body:1984] - Added empty user message after last user message at index 0 for chat model
01:22:08.650 - INFO - [send_to_browser_task:1548] - TASK [ID: 788bea43-b6b3-4081-b194-05d2044a9956]: Sending payload and 0 file(s) to browser.
01:22:08.651 - INFO - [send_to_browser_task:1553] - TASK [ID: 788bea43-b6b3-4081-b194-05d2044a9956]: Payload sent and marked as sent to browser.
01:22:08.652 - INFO - [stream_generator:1597] - STREAMER [ID: 788bea43-b6b3-4081-b194-05d2044a9956]: Generator started for model type 'chat'.
01:22:08.754 - INFO - [complete_request:841] - REQUEST_MGR: Request 788bea43-b6b3-4081-b194-05d2044a9956 completed and removed
01:22:08.755 - ERROR - [stream_generator:1670] - STREAMER [ID: 788bea43-b6b3-4081-b194-05d2044a9956]: Server returned error: {'error': 'Authentication request failed with status 403'}
01:22:08.755 - INFO - [stream_generator:1909] - GENERATOR [ID: 788bea43-b6b3-4081-b194-05d2044a9956]: Cleaned up response channel.
01:22:23.368 - INFO - [add_request:794] - REQUEST_MGR: Added request 5893b068-25c6-475d-aadf-dff547ed4ad0 for tracking
01:22:23.368 - INFO - [chat_completions:1481] - API [ID: 5893b068-25c6-475d-aadf-dff547ed4ad0]: Created persistent request for model type 'chat'.
01:22:23.369 - INFO - [chat_completions:1496] - API [ID: 5893b068-25c6-475d-aadf-dff547ed4ad0]: Returning text/event-stream response to client.
01:22:23.369 - INFO - [create_lmarena_request_body:1984] - Added empty user message after last user message at index 0 for chat model
01:22:23.369 - INFO - [send_to_browser_task:1548] - TASK [ID: 5893b068-25c6-475d-aadf-dff547ed4ad0]: Sending payload and 0 file(s) to browser.
01:22:23.370 - INFO - [send_to_browser_task:1553] - TASK [ID: 5893b068-25c6-475d-aadf-dff547ed4ad0]: Payload sent and marked as sent to browser.
01:22:23.371 - INFO - [stream_generator:1597] - STREAMER [ID: 5893b068-25c6-475d-aadf-dff547ed4ad0]: Generator started for model type 'chat'.
01:22:24.497 - INFO - [complete_request:841] - REQUEST_MGR: Request 5893b068-25c6-475d-aadf-dff547ed4ad0 completed and removed
01:22:24.497 - ERROR - [stream_generator:1670] - STREAMER [ID: 5893b068-25c6-475d-aadf-dff547ed4ad0]: Server returned error: {'error': 'Authentication verification failed with status 404'}
01:22:24.498 - INFO - [stream_generator:1909] - GENERATOR [ID: 5893b068-25c6-475d-aadf-dff547ed4ad0]: Cleaned up response channel.
01:23:07.828 - WARNING - [websocket_endpoint:1381] - ❌ 浏览器客户端已断开连接
01:23:07.828 - INFO - [websocket_endpoint:1401] - WebSocket cleaned up. Persistent requests kept alive.
01:23:08.420 - INFO - [websocket_endpoint:1278] - ✅ 浏览器WebSocket已连接
01:23:08.550 - INFO - [websocket_endpoint:1313] - 🤝 收到重连握手，浏览器有 0 个待处理请求
01:23:10.668 - ERROR - [start_heartbeat:543] - 心跳发送失败: Unexpected ASGI message 'websocket.send', after sending 'websocket.close' or response already completed.
01:23:14.034 - INFO - [add_request:794] - REQUEST_MGR: Added request 3040c03e-af49-410d-92ec-11bda51413c9 for tracking
01:23:14.034 - INFO - [chat_completions:1481] - API [ID: 3040c03e-af49-410d-92ec-11bda51413c9]: Created persistent request for model type 'chat'.
01:23:14.034 - INFO - [chat_completions:1496] - API [ID: 3040c03e-af49-410d-92ec-11bda51413c9]: Returning text/event-stream response to client.
01:23:14.034 - INFO - [create_lmarena_request_body:1984] - Added empty user message after last user message at index 1 for chat model
01:23:14.034 - INFO - [send_to_browser_task:1548] - TASK [ID: 3040c03e-af49-410d-92ec-11bda51413c9]: Sending payload and 0 file(s) to browser.
01:23:14.035 - INFO - [send_to_browser_task:1553] - TASK [ID: 3040c03e-af49-410d-92ec-11bda51413c9]: Payload sent and marked as sent to browser.
01:23:14.035 - INFO - [stream_generator:1597] - STREAMER [ID: 3040c03e-af49-410d-92ec-11bda51413c9]: Generator started for model type 'chat'.
01:23:24.216 - INFO - [complete_request:841] - REQUEST_MGR: Request 3040c03e-af49-410d-92ec-11bda51413c9 completed and removed
01:23:24.217 - ERROR - [stream_generator:1670] - STREAMER [ID: 3040c03e-af49-410d-92ec-11bda51413c9]: Server returned error: {'error': 'Authentication verification failed with status 404'}
01:23:24.217 - INFO - [stream_generator:1909] - GENERATOR [ID: 3040c03e-af49-410d-92ec-11bda51413c9]: Cleaned up response channel.
01:23:32.768 - INFO - [add_request:794] - REQUEST_MGR: Added request eec4531d-c9b4-4749-bd28-7371624800e1 for tracking
01:23:32.768 - INFO - [chat_completions:1481] - API [ID: eec4531d-c9b4-4749-bd28-7371624800e1]: Created persistent request for model type 'chat'.
01:23:32.768 - INFO - [chat_completions:1496] - API [ID: eec4531d-c9b4-4749-bd28-7371624800e1]: Returning text/event-stream response to client.
01:23:32.769 - INFO - [create_lmarena_request_body:1984] - Added empty user message after last user message at index 1 for chat model
01:23:32.769 - INFO - [send_to_browser_task:1548] - TASK [ID: eec4531d-c9b4-4749-bd28-7371624800e1]: Sending payload and 0 file(s) to browser.
01:23:32.769 - INFO - [send_to_browser_task:1553] - TASK [ID: eec4531d-c9b4-4749-bd28-7371624800e1]: Payload sent and marked as sent to browser.
01:23:32.769 - INFO - [stream_generator:1597] - STREAMER [ID: eec4531d-c9b4-4749-bd28-7371624800e1]: Generator started for model type 'chat'.
01:23:33.549 - INFO - [complete_request:841] - REQUEST_MGR: Request eec4531d-c9b4-4749-bd28-7371624800e1 completed and removed
01:23:33.549 - ERROR - [stream_generator:1670] - STREAMER [ID: eec4531d-c9b4-4749-bd28-7371624800e1]: Server returned error: {'error': 'Authentication request failed with status 500'}
01:23:33.550 - INFO - [stream_generator:1909] - GENERATOR [ID: eec4531d-c9b4-4749-bd28-7371624800e1]: Cleaned up response channel.
01:24:00.641 - INFO - [add_request:794] - REQUEST_MGR: Added request 7055d2e3-f0da-4aae-ac42-b9ca870f00db for tracking
01:24:00.641 - INFO - [chat_completions:1481] - API [ID: 7055d2e3-f0da-4aae-ac42-b9ca870f00db]: Created persistent request for model type 'chat'.
01:24:00.641 - INFO - [chat_completions:1496] - API [ID: 7055d2e3-f0da-4aae-ac42-b9ca870f00db]: Returning text/event-stream response to client.
01:24:00.642 - INFO - [create_lmarena_request_body:1984] - Added empty user message after last user message at index 1 for chat model
01:24:00.642 - INFO - [send_to_browser_task:1548] - TASK [ID: 7055d2e3-f0da-4aae-ac42-b9ca870f00db]: Sending payload and 0 file(s) to browser.
01:24:00.642 - INFO - [send_to_browser_task:1553] - TASK [ID: 7055d2e3-f0da-4aae-ac42-b9ca870f00db]: Payload sent and marked as sent to browser.
01:24:00.643 - INFO - [stream_generator:1597] - STREAMER [ID: 7055d2e3-f0da-4aae-ac42-b9ca870f00db]: Generator started for model type 'chat'.
01:24:01.220 - INFO - [complete_request:841] - REQUEST_MGR: Request 7055d2e3-f0da-4aae-ac42-b9ca870f00db completed and removed
01:24:01.220 - ERROR - [stream_generator:1670] - STREAMER [ID: 7055d2e3-f0da-4aae-ac42-b9ca870f00db]: Server returned error: {'error': 'Authentication request failed with status 500'}
01:24:01.220 - INFO - [stream_generator:1909] - GENERATOR [ID: 7055d2e3-f0da-4aae-ac42-b9ca870f00db]: Cleaned up response channel.
01:24:26.086 - WARNING - [websocket_endpoint:1381] - ❌ 浏览器客户端已断开连接
01:24:26.086 - INFO - [websocket_endpoint:1401] - WebSocket cleaned up. Persistent requests kept alive.
01:24:26.676 - INFO - [websocket_endpoint:1278] - ✅ 浏览器WebSocket已连接
01:24:26.724 - INFO - [websocket_endpoint:1313] - 🤝 收到重连握手，浏览器有 0 个待处理请求
01:24:34.055 - INFO - [add_request:794] - REQUEST_MGR: Added request 342b45a1-1e03-4af7-95e4-ef363904a15a for tracking
01:24:34.055 - INFO - [chat_completions:1481] - API [ID: 342b45a1-1e03-4af7-95e4-ef363904a15a]: Created persistent request for model type 'chat'.
01:24:34.055 - INFO - [chat_completions:1496] - API [ID: 342b45a1-1e03-4af7-95e4-ef363904a15a]: Returning text/event-stream response to client.
01:24:34.056 - INFO - [create_lmarena_request_body:1984] - Added empty user message after last user message at index 2 for chat model
01:24:34.056 - INFO - [send_to_browser_task:1548] - TASK [ID: 342b45a1-1e03-4af7-95e4-ef363904a15a]: Sending payload and 0 file(s) to browser.
01:24:34.056 - INFO - [send_to_browser_task:1553] - TASK [ID: 342b45a1-1e03-4af7-95e4-ef363904a15a]: Payload sent and marked as sent to browser.
01:24:34.058 - INFO - [stream_generator:1597] - STREAMER [ID: 342b45a1-1e03-4af7-95e4-ef363904a15a]: Generator started for model type 'chat'.
01:24:38.461 - ERROR - [start_heartbeat:543] - 心跳发送失败: Unexpected ASGI message 'websocket.send', after sending 'websocket.close' or response already completed.
01:25:06.700 - WARNING - [cleanup_old_requests:688] - 清理超时请求: 8db9ff45-b017-4978-97d7-643ee15164e4
01:25:06.700 - WARNING - [cleanup_old_requests:688] - 清理超时请求: 0dddf016-9111-4186-9baf-427dd110d2e5
01:25:06.701 - WARNING - [cleanup_old_requests:688] - 清理超时请求: 1a3027d5-c775-4ef0-a50e-93003804ed69
01:25:06.701 - WARNING - [cleanup_old_requests:688] - 清理超时请求: e7015d24-30b0-4f90-a22b-75ccb86753dc
01:25:06.701 - WARNING - [cleanup_old_requests:688] - 清理超时请求: da1ecb52-0bc7-40b8-a4a2-931db33271e5
01:25:06.702 - INFO - [periodic_cleanup:708] - 清理任务执行完成. 活跃请求: 6
01:25:21.339 - WARNING - [websocket_endpoint:1381] - ❌ 浏览器客户端已断开连接
01:25:21.339 - WARNING - [handle_browser_disconnect:873] - REQUEST_MGR: Browser disconnected with 1 pending requests.
01:25:21.339 - INFO - [handle_browser_disconnect:885] - REQUEST_MGR: Spawning timeout watcher for 1 pending requests.
01:25:21.340 - INFO - [websocket_endpoint:1401] - WebSocket cleaned up. Persistent requests kept alive.
01:25:21.478 - WARNING - [websocket_endpoint:1381] - ❌ 浏览器客户端已断开连接
01:25:21.478 - WARNING - [handle_browser_disconnect:873] - REQUEST_MGR: Browser disconnected with 1 pending requests.
01:25:21.478 - INFO - [handle_browser_disconnect:885] - REQUEST_MGR: Spawning timeout watcher for 1 pending requests.
01:25:21.479 - INFO - [websocket_endpoint:1401] - WebSocket cleaned up. Persistent requests kept alive.
01:25:21.537 - WARNING - [websocket_endpoint:1381] - ❌ 浏览器客户端已断开连接
01:25:21.537 - WARNING - [handle_browser_disconnect:873] - REQUEST_MGR: Browser disconnected with 1 pending requests.
01:25:21.538 - INFO - [handle_browser_disconnect:885] - REQUEST_MGR: Spawning timeout watcher for 1 pending requests.
01:25:21.538 - INFO - [websocket_endpoint:1401] - WebSocket cleaned up. Persistent requests kept alive.
01:25:21.636 - WARNING - [websocket_endpoint:1381] - ❌ 浏览器客户端已断开连接
01:25:21.637 - WARNING - [handle_browser_disconnect:873] - REQUEST_MGR: Browser disconnected with 1 pending requests.
01:25:21.637 - INFO - [handle_browser_disconnect:885] - REQUEST_MGR: Spawning timeout watcher for 1 pending requests.
01:25:21.637 - INFO - [websocket_endpoint:1401] - WebSocket cleaned up. Persistent requests kept alive.
01:25:26.699 - ERROR - [start_heartbeat:543] - 心跳发送失败: Unexpected ASGI message 'websocket.send', after sending 'websocket.close' or response already completed.
01:25:35.290 - INFO - [lifespan:1173] - 服务器正在启动...
01:25:35.291 - INFO - [lifespan:1178] - 🌐 Server access URLs:
01:25:35.292 - INFO - [lifespan:1179] -   - Local: http://localhost:9080
01:25:35.292 - INFO - [lifespan:1180] -   - Network: http://127.0.0.1:9080
01:25:35.292 - INFO - [lifespan:1181] - 📱 Use the Network URL to access from your phone on the same WiFi
01:25:35.292 - INFO - [lifespan:1184] - 
📋 Available Endpoints:
01:25:35.292 - INFO - [lifespan:1185] -   🖥️  Monitor Dashboard: http://127.0.0.1:9080/monitor
01:25:35.292 - INFO - [lifespan:1186] -      实时监控面板，查看系统状态、请求日志、性能指标
01:25:35.293 - INFO - [lifespan:1188] - 
  📊 Metrics & Health:
01:25:35.293 - INFO - [lifespan:1189] -      - Prometheus Metrics: http://127.0.0.1:9080/metrics
01:25:35.293 - INFO - [lifespan:1190] -        Prometheus格式的性能指标，可接入Grafana
01:25:35.293 - INFO - [lifespan:1191] -      - Health Check: http://127.0.0.1:9080/health
01:25:35.293 - INFO - [lifespan:1192] -        基础健康检查
01:25:35.293 - INFO - [lifespan:1193] -      - Detailed Health: http://127.0.0.1:9080/api/health/detailed
01:25:35.293 - INFO - [lifespan:1194] -        详细健康状态，包含评分和建议
01:25:35.293 - INFO - [lifespan:1196] - 
  🤖 AI API:
01:25:35.294 - INFO - [lifespan:1197] -      - Chat Completions: POST http://127.0.0.1:9080/v1/chat/completions
01:25:35.294 - INFO - [lifespan:1198] -        OpenAI兼容的聊天API
01:25:35.294 - INFO - [lifespan:1199] -      - List Models: GET http://127.0.0.1:9080/v1/models
01:25:35.294 - INFO - [lifespan:1200] -        获取可用模型列表
01:25:35.294 - INFO - [lifespan:1201] -      - Refresh Models: POST http://127.0.0.1:9080/v1/refresh-models
01:25:35.294 - INFO - [lifespan:1202] -        刷新模型列表
01:25:35.294 - INFO - [lifespan:1204] - 
  📈 Statistics:
01:25:35.294 - INFO - [lifespan:1205] -      - Stats Summary: http://127.0.0.1:9080/api/stats/summary
01:25:35.294 - INFO - [lifespan:1206] -        24小时统计摘要
01:25:35.295 - INFO - [lifespan:1207] -      - Request Logs: http://127.0.0.1:9080/api/logs/requests
01:25:35.295 - INFO - [lifespan:1208] -        请求日志API
01:25:35.295 - INFO - [lifespan:1209] -      - Error Logs: http://127.0.0.1:9080/api/logs/errors
01:25:35.295 - INFO - [lifespan:1210] -        错误日志API
01:25:35.295 - INFO - [lifespan:1211] -      - Alerts: http://127.0.0.1:9080/api/alerts
01:25:35.295 - INFO - [lifespan:1212] -        系统告警历史
01:25:35.295 - INFO - [lifespan:1214] - 
  🛠️  OpenAI Client Config:
01:25:35.295 - INFO - [lifespan:1215] -      base_url='http://127.0.0.1:9080/v1'
01:25:35.296 - INFO - [lifespan:1216] -      api_key='sk-any-string-you-like'
01:25:35.296 - INFO - [lifespan:1217] - 
============================================================

01:25:35.296 - INFO - [lifespan:1222] - 已加载 85 个备用模型
01:25:35.296 - INFO - [lifespan:1231] - 服务器启动完成
01:25:35.297 - INFO - [periodic_cleanup:708] - 清理任务执行完成. 活跃请求: 0
01:25:35.298 - INFO - [lifespan:1238] - 生命周期: 服务器正在关闭。正在取消 2 个后台任务...
01:25:35.298 - INFO - [lifespan:1244] - 生命周期: 正在取消任务: <Task pending name='Task-4' coro=<MonitoringAlerts.check_system_health() running at C:\Users\<USER>\Downloads\lmarena-proxy\lmarena-proxy-main\proxy_server.py:578> wait_for=<Future pending cb=[Task.task_wakeup()]>>
01:25:35.298 - INFO - [lifespan:1244] - 生命周期: 正在取消任务: <Task pending name='Task-3' coro=<periodic_cleanup() running at C:\Users\<USER>\Downloads\lmarena-proxy\lmarena-proxy-main\proxy_server.py:713> wait_for=<Future pending cb=[Task.task_wakeup()]>>
01:25:35.298 - INFO - [lifespan:1250] - 生命周期: 等待 2 个已取消的任务完成...
01:25:35.299 - INFO - [lifespan:1256] - 生命周期: 任务 0 正常完成
01:25:35.299 - INFO - [lifespan:1256] - 生命周期: 任务 1 正常完成
01:25:35.299 - INFO - [lifespan:1258] - 生命周期: 所有后台任务已取消。关闭完成。
01:25:37.276 - ERROR - [start_heartbeat:543] - 心跳发送失败: Unexpected ASGI message 'websocket.send', after sending 'websocket.close' or response already completed.
01:25:37.276 - ERROR - [start_heartbeat:543] - 心跳发送失败: Unexpected ASGI message 'websocket.send', after sending 'websocket.close' or response already completed.
01:25:41.239 - INFO - [websocket_endpoint:1278] - ✅ 浏览器WebSocket已连接
01:25:41.239 - INFO - [websocket_endpoint:1291] - 🔄 浏览器重连，有 1 个待处理请求
01:25:41.279 - INFO - [websocket_endpoint:1313] - 🤝 收到重连握手，浏览器有 0 个待处理请求
01:25:42.594 - ERROR - [start_heartbeat:543] - 心跳发送失败: Unexpected ASGI message 'websocket.send', after sending 'websocket.close' or response already completed.
01:25:47.762 - WARNING - [stream_generator:1871] - GENERATOR [ID: 342b45a1-1e03-4af7-95e4-ef363904a15a]: Client disconnected.
01:25:47.763 - INFO - [stream_generator:1880] - GENERATOR [ID: 342b45a1-1e03-4af7-95e4-ef363904a15a]: Sent abort message to browser
01:25:47.763 - INFO - [complete_request:841] - REQUEST_MGR: Request 342b45a1-1e03-4af7-95e4-ef363904a15a completed and removed
01:25:51.166 - INFO - [add_request:794] - REQUEST_MGR: Added request 75b06cbc-9ef7-462f-8bb2-12115e94a1d4 for tracking
01:25:51.166 - INFO - [chat_completions:1481] - API [ID: 75b06cbc-9ef7-462f-8bb2-12115e94a1d4]: Created persistent request for model type 'chat'.
01:25:51.166 - INFO - [chat_completions:1496] - API [ID: 75b06cbc-9ef7-462f-8bb2-12115e94a1d4]: Returning text/event-stream response to client.
01:25:51.167 - INFO - [create_lmarena_request_body:1984] - Added empty user message after last user message at index 3 for chat model
01:25:51.167 - INFO - [send_to_browser_task:1548] - TASK [ID: 75b06cbc-9ef7-462f-8bb2-12115e94a1d4]: Sending payload and 0 file(s) to browser.
01:25:51.169 - INFO - [send_to_browser_task:1553] - TASK [ID: 75b06cbc-9ef7-462f-8bb2-12115e94a1d4]: Payload sent and marked as sent to browser.
01:25:51.170 - INFO - [stream_generator:1597] - STREAMER [ID: 75b06cbc-9ef7-462f-8bb2-12115e94a1d4]: Generator started for model type 'chat'.
01:25:58.488 - INFO - [complete_request:841] - REQUEST_MGR: Request 75b06cbc-9ef7-462f-8bb2-12115e94a1d4 completed and removed
01:25:58.493 - INFO - [stream_generator:1909] - GENERATOR [ID: 75b06cbc-9ef7-462f-8bb2-12115e94a1d4]: Cleaned up response channel.
01:26:18.263 - INFO - [add_request:794] - REQUEST_MGR: Added request a51e234c-1ea7-46ae-879f-c280b230dc42 for tracking
01:26:18.263 - INFO - [chat_completions:1481] - API [ID: a51e234c-1ea7-46ae-879f-c280b230dc42]: Created persistent request for model type 'chat'.
01:26:18.263 - INFO - [chat_completions:1496] - API [ID: a51e234c-1ea7-46ae-879f-c280b230dc42]: Returning text/event-stream response to client.
01:26:18.264 - INFO - [create_lmarena_request_body:1984] - Added empty user message after last user message at index 0 for chat model
01:26:18.264 - INFO - [send_to_browser_task:1548] - TASK [ID: a51e234c-1ea7-46ae-879f-c280b230dc42]: Sending payload and 0 file(s) to browser.
01:26:18.264 - INFO - [send_to_browser_task:1553] - TASK [ID: a51e234c-1ea7-46ae-879f-c280b230dc42]: Payload sent and marked as sent to browser.
01:26:18.265 - INFO - [stream_generator:1597] - STREAMER [ID: a51e234c-1ea7-46ae-879f-c280b230dc42]: Generator started for model type 'chat'.
01:26:21.544 - INFO - [complete_request:841] - REQUEST_MGR: Request a51e234c-1ea7-46ae-879f-c280b230dc42 completed and removed
01:26:21.565 - INFO - [stream_generator:1909] - GENERATOR [ID: a51e234c-1ea7-46ae-879f-c280b230dc42]: Cleaned up response channel.
01:26:25.756 - INFO - [add_request:794] - REQUEST_MGR: Added request 978a75c9-8655-44cc-a8ee-fd21f4b01b4e for tracking
01:26:25.756 - INFO - [chat_completions:1481] - API [ID: 978a75c9-8655-44cc-a8ee-fd21f4b01b4e]: Created persistent request for model type 'chat'.
01:26:25.757 - INFO - [chat_completions:1496] - API [ID: 978a75c9-8655-44cc-a8ee-fd21f4b01b4e]: Returning text/event-stream response to client.
01:26:25.757 - INFO - [create_lmarena_request_body:1984] - Added empty user message after last user message at index 4 for chat model
01:26:25.758 - INFO - [send_to_browser_task:1548] - TASK [ID: 978a75c9-8655-44cc-a8ee-fd21f4b01b4e]: Sending payload and 0 file(s) to browser.
01:26:25.759 - INFO - [send_to_browser_task:1553] - TASK [ID: 978a75c9-8655-44cc-a8ee-fd21f4b01b4e]: Payload sent and marked as sent to browser.
01:26:25.760 - INFO - [stream_generator:1597] - STREAMER [ID: 978a75c9-8655-44cc-a8ee-fd21f4b01b4e]: Generator started for model type 'chat'.
01:26:29.475 - INFO - [complete_request:841] - REQUEST_MGR: Request 978a75c9-8655-44cc-a8ee-fd21f4b01b4e completed and removed
01:26:29.500 - INFO - [stream_generator:1909] - GENERATOR [ID: 978a75c9-8655-44cc-a8ee-fd21f4b01b4e]: Cleaned up response channel.
01:28:21.356 - INFO - [request_timeout_watcher:855] - WATCHER: Timeout reached. Checking 1 requests.
01:28:21.497 - INFO - [request_timeout_watcher:855] - WATCHER: Timeout reached. Checking 1 requests.
01:28:21.544 - INFO - [request_timeout_watcher:855] - WATCHER: Timeout reached. Checking 1 requests.
01:28:21.637 - INFO - [request_timeout_watcher:855] - WATCHER: Timeout reached. Checking 1 requests.
01:30:06.706 - WARNING - [cleanup_old_requests:688] - 清理超时请求: 788bea43-b6b3-4081-b194-05d2044a9956
01:30:06.706 - WARNING - [cleanup_old_requests:688] - 清理超时请求: 5893b068-25c6-475d-aadf-dff547ed4ad0
01:30:06.706 - WARNING - [cleanup_old_requests:688] - 清理超时请求: 3040c03e-af49-410d-92ec-11bda51413c9
01:30:06.707 - WARNING - [cleanup_old_requests:688] - 清理超时请求: eec4531d-c9b4-4749-bd28-7371624800e1
01:30:06.707 - WARNING - [cleanup_old_requests:688] - 清理超时请求: 7055d2e3-f0da-4aae-ac42-b9ca870f00db
01:30:06.707 - WARNING - [cleanup_old_requests:688] - 清理超时请求: 342b45a1-1e03-4af7-95e4-ef363904a15a
01:30:06.708 - INFO - [periodic_cleanup:708] - 清理任务执行完成. 活跃请求: 0
01:33:52.850 - INFO - [add_request:794] - REQUEST_MGR: Added request f55ed99c-a47f-4d53-ae14-92adf1de645a for tracking
01:33:52.850 - INFO - [chat_completions:1481] - API [ID: f55ed99c-a47f-4d53-ae14-92adf1de645a]: Created persistent request for model type 'chat'.
01:33:52.850 - INFO - [chat_completions:1496] - API [ID: f55ed99c-a47f-4d53-ae14-92adf1de645a]: Returning text/event-stream response to client.
01:33:52.853 - INFO - [create_lmarena_request_body:1984] - Added empty user message after last user message at index 5 for chat model
01:33:52.853 - INFO - [send_to_browser_task:1548] - TASK [ID: f55ed99c-a47f-4d53-ae14-92adf1de645a]: Sending payload and 0 file(s) to browser.
01:33:52.853 - INFO - [send_to_browser_task:1553] - TASK [ID: f55ed99c-a47f-4d53-ae14-92adf1de645a]: Payload sent and marked as sent to browser.
01:33:52.854 - INFO - [stream_generator:1597] - STREAMER [ID: f55ed99c-a47f-4d53-ae14-92adf1de645a]: Generator started for model type 'chat'.
01:34:02.135 - INFO - [complete_request:841] - REQUEST_MGR: Request f55ed99c-a47f-4d53-ae14-92adf1de645a completed and removed
01:34:02.149 - INFO - [stream_generator:1909] - GENERATOR [ID: f55ed99c-a47f-4d53-ae14-92adf1de645a]: Cleaned up response channel.
01:35:06.717 - INFO - [periodic_cleanup:708] - 清理任务执行完成. 活跃请求: 0
01:40:06.720 - INFO - [periodic_cleanup:708] - 清理任务执行完成. 活跃请求: 0
01:44:40.864 - INFO - [lifespan:1181] - 服务器正在启动...
01:44:40.865 - INFO - [lifespan:1207] - 🌐 Server access URLs:
01:44:40.865 - INFO - [lifespan:1208] -   - Local: http://localhost:9080
01:44:40.866 - INFO - [lifespan:1209] -   - Network: http://127.0.0.1:9080
01:44:40.866 - INFO - [lifespan:1212] - 📱 Use the Network URL to access from your phone on the same WiFi
01:44:40.866 - INFO - [lifespan:1215] - 
📋 Available Endpoints:
01:44:40.866 - INFO - [lifespan:1216] -   🖥️  Monitor Dashboard: http://127.0.0.1:9080/monitor
01:44:40.866 - INFO - [lifespan:1217] -      实时监控面板，查看系统状态、请求日志、性能指标
01:44:40.866 - INFO - [lifespan:1219] - 
  📊 Metrics & Health:
01:44:40.866 - INFO - [lifespan:1220] -      - Prometheus Metrics: http://127.0.0.1:9080/metrics
01:44:40.866 - INFO - [lifespan:1221] -        Prometheus格式的性能指标，可接入Grafana
01:44:40.866 - INFO - [lifespan:1222] -      - Health Check: http://127.0.0.1:9080/health
01:44:40.866 - INFO - [lifespan:1223] -        基础健康检查
01:44:40.866 - INFO - [lifespan:1224] -      - Detailed Health: http://127.0.0.1:9080/api/health/detailed
01:44:40.866 - INFO - [lifespan:1225] -        详细健康状态，包含评分和建议
01:44:40.866 - INFO - [lifespan:1227] - 
  🤖 AI API:
01:44:40.866 - INFO - [lifespan:1228] -      - Chat Completions: POST http://127.0.0.1:9080/v1/chat/completions
01:44:40.866 - INFO - [lifespan:1229] -        OpenAI兼容的聊天API
01:44:40.867 - INFO - [lifespan:1230] -      - List Models: GET http://127.0.0.1:9080/v1/models
01:44:40.867 - INFO - [lifespan:1231] -        获取可用模型列表
01:44:40.867 - INFO - [lifespan:1232] -      - Refresh Models: POST http://127.0.0.1:9080/v1/refresh-models
01:44:40.867 - INFO - [lifespan:1233] -        刷新模型列表
01:44:40.867 - INFO - [lifespan:1235] - 
  📈 Statistics:
01:44:40.867 - INFO - [lifespan:1236] -      - Stats Summary: http://127.0.0.1:9080/api/stats/summary
01:44:40.867 - INFO - [lifespan:1237] -        24小时统计摘要
01:44:40.867 - INFO - [lifespan:1238] -      - Request Logs: http://127.0.0.1:9080/api/logs/requests
01:44:40.867 - INFO - [lifespan:1239] -        请求日志API
01:44:40.867 - INFO - [lifespan:1240] -      - Error Logs: http://127.0.0.1:9080/api/logs/errors
01:44:40.867 - INFO - [lifespan:1241] -        错误日志API
01:44:40.867 - INFO - [lifespan:1242] -      - Alerts: http://127.0.0.1:9080/api/alerts
01:44:40.867 - INFO - [lifespan:1243] -        系统告警历史
01:44:40.867 - INFO - [lifespan:1245] - 
  🛠️  OpenAI Client Config:
01:44:40.867 - INFO - [lifespan:1246] -      base_url='http://127.0.0.1:9080/v1'
01:44:40.868 - INFO - [lifespan:1247] -      api_key='sk-any-string-you-like'
01:44:40.868 - INFO - [lifespan:1248] - 
============================================================

01:44:40.868 - INFO - [lifespan:1253] - 已加载 85 个备用模型
01:44:40.868 - INFO - [lifespan:1262] - 服务器启动完成
01:44:40.868 - INFO - [periodic_cleanup:716] - 清理任务执行完成. 活跃请求: 0
01:44:46.474 - INFO - [websocket_endpoint:1316] - ✅ 浏览器WebSocket已连接
01:44:46.475 - INFO - [websocket_endpoint:1351] - 🤝 收到重连握手，浏览器有 0 个待处理请求
01:46:12.662 - INFO - [save_config:120] - 配置已保存
01:46:15.613 - INFO - [save_config:120] - 配置已保存
