#!/bin/bash

echo "========================================"
echo "  安装 LMArena 代理服务器依赖"
echo "========================================"
echo

echo "检查 Python 环境..."
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "错误: Python 未安装"
        echo "请安装 Python 3.8+ 并重试"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo "✅ Python 环境正常"
echo

echo "升级 pip..."
$PYTHON_CMD -m pip install --upgrade pip

echo
echo "安装项目依赖..."
$PYTHON_CMD -m pip install -r requirements.txt

echo
echo "检查可选依赖 (Node.js for LocalTunnel)..."
if command -v node &> /dev/null; then
    echo "✅ Node.js 已安装，LocalTunnel 可用"
    if npm install -g localtunnel &> /dev/null; then
        echo "✅ LocalTunnel 已安装"
    else
        echo "⚠️ LocalTunnel 安装失败，但不影响主要功能"
    fi
else
    echo "⚠️ Node.js 未安装，LocalTunnel 不可用"
    echo "如需使用 LocalTunnel，请安装 Node.js: https://nodejs.org"
fi

echo
echo "========================================"
echo "依赖安装完成！"
echo "========================================"
echo
echo "现在可以运行服务器:"
echo "  $PYTHON_CMD proxy_server.py"
echo
echo "或使用启动脚本:"
echo "  ./start.sh"
echo
